defmodule Mix.Tasks.Drops.Relation.Gen do
  @moduledoc """
  Generates Drops.Relation modules from database table introspection.

  This task introspects database tables and generates minimal relation modules
  that use Drops.Relation with the appropriate repo and table name configuration.

  ## Usage

      mix drops.relation.gen [options]

  ## Options

    * `--namespace` - The namespace for generated relations (e.g., "MyApp.Relations")
    * `--repo` - The Ecto repository module (e.g., "MyApp.Repo")
    * `--app` - The application name to infer defaults from (e.g., "MyApp")
    * `--sync` - Whether to sync/update existing files (default: true)
    * `--tables` - Comma-separated list of specific tables to generate relations for

  ## Examples

      # Generate relations for all tables with default settings
      mix drops.relation.gen --app MyApp

      # Generate relations with custom namespace
      mix drops.relation.gen --namespace MyApp.Relations --app MyApp

      # Generate relations for specific tables only
      mix drops.relation.gen --tables users,posts --app MyApp

      # Overwrite existing files instead of syncing
      mix drops.relation.gen --app MyApp --sync false
  """

  use Igniter.Mix.Task

  alias Drops.Relation.Generator
  alias Igniter.Project.Module

  require Sourceror.Zipper

  @impl Igniter.Mix.Task
  def info(_argv, _composing_task) do
    %Igniter.Mix.Task.Info{
      group: :drops_relation,
      example: "mix drops.relation.gen --app MyApp",
      positional: [],
      schema: [
        namespace: :string,
        repo: :string,
        app: :string,
        sync: :boolean,
        tables: :string,
        help: :boolean
      ],
      aliases: [
        n: :namespace,
        r: :repo,
        a: :app,
        s: :sync,
        t: :tables,
        h: :help
      ]
    }
  end

  @impl Igniter.Mix.Task
  def igniter(igniter) do
    options = validate_and_parse_options(igniter.args.argv)

    # Make Igniter non-interactive for testing
    igniter =
      igniter
      |> Igniter.assign(:prompt_on_git_changes?, false)
      |> Igniter.assign(:quiet_on_no_changes?, true)

    # Ensure the application is started before proceeding
    ensure_application_started(options[:app])

    # Get list of tables to process
    tables = get_tables_to_process(options)

    if Enum.empty?(tables) do
      Mix.shell().info("No tables found to generate relations for.")
      igniter
    else
      Mix.shell().info("Generating relations for tables: #{Enum.join(tables, ", ")}")

      # Generate relation files for each table
      Enum.reduce(tables, igniter, fn table, acc_igniter ->
        generate_relation_file(acc_igniter, table, options)
      end)
    end
  end

  # Private functions

  defp validate_and_parse_options(argv) do
    {parsed, _remaining, _invalid} =
      OptionParser.parse(argv,
        strict: [
          namespace: :string,
          repo: :string,
          app: :string,
          sync: :boolean,
          tables: :string,
          help: :boolean
        ]
      )

    options = Map.new(parsed)

    # Handle help option
    if options[:help] do
      Mix.shell().info(@moduledoc)
      System.halt(0)
    end

    # Validate required options
    unless options[:app] do
      Mix.raise("--app option is required")
    end

    # Set defaults based on app name
    app_name = options[:app]
    namespace = options[:namespace] || "#{app_name}.Relations"

    options
    |> Map.put(:namespace, namespace)
    |> Map.put_new(:repo, "#{app_name}.Repo")
    |> Map.put_new(:sync, true)
  end

  defp ensure_application_started(app_name) do
    app_atom = String.to_atom(Macro.underscore(app_name))

    case Application.ensure_all_started(app_atom) do
      {:ok, _} ->
        :ok

      {:error, {app, reason}} ->
        Mix.shell().error(
          "Failed to start application #{app}: #{inspect(reason)}. " <>
            "Make sure your application is properly configured and dependencies are available."
        )

        System.halt(1)
    end
  end

  defp get_tables_to_process(options) do
    case options[:tables] do
      nil ->
        # Get all tables from the database
        get_all_tables(options)

      tables_string ->
        # Parse comma-separated list of tables
        tables_string
        |> String.split(",")
        |> Enum.map(&String.trim/1)
        |> Enum.reject(&(&1 == ""))
    end
  end

  defp get_all_tables(options) do
    repo_name = options[:repo]

    try do
      repo = String.to_existing_atom("Elixir.#{repo_name}")

      # Use database introspection to get table names
      case repo.__adapter__() do
        Ecto.Adapters.SQLite3 ->
          get_sqlite_tables(repo)

        Ecto.Adapters.Postgres ->
          get_postgres_tables(repo)

        _ ->
          Mix.shell().error("Unsupported database adapter for table introspection")
          []
      end
    rescue
      error ->
        Mix.shell().error("Failed to introspect database tables: #{inspect(error)}")
        []
    end
  end

  defp get_sqlite_tables(repo) do
    query = """
    SELECT name
    FROM sqlite_master
    WHERE type = 'table'
    AND name NOT LIKE 'sqlite_%'
    AND name NOT IN ('schema_migrations')
    ORDER BY name
    """

    case Ecto.Adapters.SQL.query(repo, query, []) do
      {:ok, %{rows: rows}} ->
        Enum.map(rows, fn [table_name] -> table_name end)

      {:error, error} ->
        Mix.shell().error("Failed to query SQLite tables: #{inspect(error)}")
        []
    end
  end

  defp get_postgres_tables(repo) do
    query = """
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name != 'schema_migrations'
    ORDER BY table_name
    """

    case Ecto.Adapters.SQL.query(repo, query, []) do
      {:ok, %{rows: rows}} ->
        Enum.map(rows, fn [table_name] -> table_name end)

      {:error, error} ->
        Mix.shell().error("Failed to query PostgreSQL tables: #{inspect(error)}")
        []
    end
  end

  defp generate_relation_file(igniter, table, options) do
    namespace = options[:namespace]

    # Convert table name to module name (e.g., "users" -> "Users")
    module_name_part =
      table
      |> String.split("_")
      |> Enum.map(&String.capitalize/1)
      |> Enum.join("")

    module_name_string = "#{namespace}.#{module_name_part}"
    module_name = Module.parse(module_name_string)

    try do
      # Generate the relation body content (without defmodule wrapper) for Igniter
      relation_content =
        Generator.generate_relation_module_body(table, module_name_string, options)

      Mix.shell().info("Creating or updating relation: #{module_name_string}")

      Module.find_and_update_or_create_module(
        igniter,
        module_name,
        relation_content,
        fn zipper ->
          if options[:sync] do
            # Sync mode: preserve custom code and only update relation-related parts
            update_relation_preserving_custom_code(zipper, table, module_name_string, options)
          else
            # Non-sync mode: replace entire module content
            replace_entire_module_content(zipper, relation_content)
          end
        end
      )
    rescue
      error ->
        Mix.shell().error("Failed to generate relation for table '#{table}': #{inspect(error)}")
        igniter
    end
  end

  # Helper function for sync mode: preserve custom code and only update relation-related parts
  defp update_relation_preserving_custom_code(zipper, _table_name, _module_name_string, _options) do
    # For relation modules, sync mode is simpler since they're minimal
    # Just return the zipper as-is for now - relations are simple enough that full replacement is fine
    zipper
  end

  # Helper function for non-sync mode: replace entire module content
  defp replace_entire_module_content(zipper, new_content) do
    # Replace the entire module body with new content
    # This is used when sync=false
    Sourceror.Zipper.replace(zipper, new_content)
  end
end
