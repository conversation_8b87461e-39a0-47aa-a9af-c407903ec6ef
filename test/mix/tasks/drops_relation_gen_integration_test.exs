defmodule Mix.Tasks.Drops.Relation.GenIntegrationTest do
  use ExUnit.Case, async: false

  @sample_app_path Path.join([__DIR__, "..", "..", "sample_app"])
  @relations_path Path.join([@sample_app_path, "lib", "sample_app", "relations"])

  setup do
    # Clean up any existing relation files before each test
    if File.exists?(@relations_path) do
      File.rm_rf!(@relations_path)
    end

    # Ensure the relations directory exists
    File.mkdir_p!(@relations_path)

    # Change to sample_app directory for mix tasks
    original_cwd = File.cwd!()
    File.cd!(@sample_app_path)

    # Set MIX_ENV to dev to avoid test database ownership issues
    original_env = System.get_env("MIX_ENV")
    System.put_env("MIX_ENV", "dev")

    on_exit(fn ->
      File.cd!(original_cwd)
      # Restore original MIX_ENV
      if original_env do
        System.put_env("MIX_ENV", original_env)
      else
        System.delete_env("MIX_ENV")
      end

      # Clean up after test
      if File.exists?(@relations_path) do
        File.rm_rf!(@relations_path)
      end
    end)

    :ok
  end

  describe "gen relation mix task integration" do
    test "generates relation files for all tables with --yes option" do
      # Run the mix task with --yes to avoid prompts
      {output, exit_code} =
        System.cmd(
          "mix",
          [
            "drops.relation.gen",
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Relations",
            "--yes"
          ],
          env: [{"MIX_ENV", "dev"}],
          stderr_to_stdout: true
        )

      assert exit_code == 0, "Mix task failed with output: #{output}"

      # Verify the task ran successfully
      assert output =~ "Creating or updating relation"
      assert output =~ "SampleApp.Relations.Users"
      assert output =~ "SampleApp.Relations.Posts"
      assert output =~ "SampleApp.Relations.Comments"

      # Verify relation files were created
      user_file = Path.join(@relations_path, "users.ex")
      post_file = Path.join(@relations_path, "posts.ex")
      comment_file = Path.join(@relations_path, "comments.ex")

      assert File.exists?(user_file)
      assert File.exists?(post_file)
      assert File.exists?(comment_file)

      # Verify user relation content
      user_content = File.read!(user_file)
      assert user_content =~ "defmodule SampleApp.Relations.Users do"
      assert user_content =~ "use Drops.Relation"
      assert user_content =~ "repo: SampleApp.Repo"
      assert user_content =~ "name: \"users\""

      # Verify post relation content
      post_content = File.read!(post_file)
      assert post_content =~ "defmodule SampleApp.Relations.Posts do"
      assert post_content =~ "use Drops.Relation"
      assert post_content =~ "repo: SampleApp.Repo"
      assert post_content =~ "name: \"posts\""

      # Verify comment relation content
      comment_content = File.read!(comment_file)
      assert comment_content =~ "defmodule SampleApp.Relations.Comments do"
      assert comment_content =~ "use Drops.Relation"
      assert comment_content =~ "repo: SampleApp.Repo"
      assert comment_content =~ "name: \"comments\""
    end

    test "generates relation for specific table only" do
      # Run the mix task for users table only
      {output, exit_code} =
        System.cmd(
          "mix",
          [
            "drops.relation.gen",
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Relations",
            "--tables",
            "users",
            "--yes"
          ],
          env: [{"MIX_ENV", "dev"}],
          stderr_to_stdout: true
        )

      assert exit_code == 0, "Mix task failed with output: #{output}"

      # Verify only user relation was created
      assert output =~ "SampleApp.Relations.Users"
      refute output =~ "SampleApp.Relations.Posts"
      refute output =~ "SampleApp.Relations.Comments"

      user_file = Path.join(@relations_path, "users.ex")
      post_file = Path.join(@relations_path, "posts.ex")
      comment_file = Path.join(@relations_path, "comments.ex")

      assert File.exists?(user_file)
      refute File.exists?(post_file)
      refute File.exists?(comment_file)
    end

    test "updates existing relation file in sync mode" do
      # First, create an initial relation file with custom content
      user_file = Path.join(@relations_path, "users.ex")

      initial_content = """
      defmodule SampleApp.Relations.Users do
        use Drops.Relation, repo: SampleApp.Repo, name: "users"

        # Custom function that should be preserved
        def custom_query do
          restrict(email: "<EMAIL>")
        end
      end
      """

      File.write!(user_file, initial_content)

      # Run the mix task in sync mode
      {output, exit_code} =
        System.cmd(
          "mix",
          [
            "drops.relation.gen",
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Relations",
            "--tables",
            "users",
            "--sync",
            "--yes"
          ],
          env: [{"MIX_ENV", "dev"}],
          stderr_to_stdout: true
        )

      assert exit_code == 0, "Mix task failed with output: #{output}"

      # Verify the task ran in sync mode
      assert output =~ "Creating or updating relation"

      # Verify the file was updated (for relation modules, sync mode is simpler)
      updated_content = File.read!(user_file)

      # Should preserve custom function
      assert updated_content =~ "def custom_query"

      # Should still have the basic relation structure
      assert updated_content =~ "use Drops.Relation"
      assert updated_content =~ "repo: SampleApp.Repo"
      assert updated_content =~ "name: \"users\""
    end

    test "generated relations are valid Drops.Relation modules" do
      # Generate relations
      {output, exit_code} =
        System.cmd(
          "mix",
          [
            "drops.relation.gen",
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Relations",
            "--yes"
          ],
          env: [{"MIX_ENV", "dev"}],
          stderr_to_stdout: true
        )

      assert exit_code == 0, "Mix task failed with output: #{output}"

      # Load and verify each generated relation module
      user_file = Path.join(@relations_path, "users.ex")
      post_file = Path.join(@relations_path, "posts.ex")
      comment_file = Path.join(@relations_path, "comments.ex")

      # Compile and load the modules to verify they're valid
      # Note: Drops.Relation modules generate protocol implementations, so we get multiple modules
      user_compiled = Code.compile_file(user_file)
      post_compiled = Code.compile_file(post_file)
      comment_compiled = Code.compile_file(comment_file)

      # Extract the main relation modules (not protocol implementations)
      user_module =
        Enum.find_value(user_compiled, fn {mod, _} ->
          mod_string = Atom.to_string(mod)
          if mod_string == "SampleApp.Relations.Users", do: mod
        end)

      post_module =
        Enum.find_value(post_compiled, fn {mod, _} ->
          mod_string = Atom.to_string(mod)
          if mod_string == "SampleApp.Relations.Posts", do: mod
        end)

      comment_module =
        Enum.find_value(comment_compiled, fn {mod, _} ->
          mod_string = Atom.to_string(mod)
          if mod_string == "SampleApp.Relations.Comments", do: mod
        end)

      # Verify they implement Drops.Relation behavior
      assert function_exported?(user_module, :restrict, 2)
      assert function_exported?(post_module, :restrict, 2)
      assert function_exported?(comment_module, :restrict, 2)

      # Verify they have the expected query API functions
      assert function_exported?(user_module, :all, 0)
      assert function_exported?(post_module, :all, 0)
      assert function_exported?(comment_module, :all, 0)

      assert function_exported?(user_module, :get, 1)
      assert function_exported?(post_module, :get, 1)
      assert function_exported?(comment_module, :get, 1)
    end

    test "handles multiple tables with complex names" do
      # Run the mix task for multiple tables including one with underscores
      {output, exit_code} =
        System.cmd(
          "mix",
          [
            "drops.relation.gen",
            "--app",
            "SampleApp",
            "--repo",
            "SampleApp.Repo",
            "--namespace",
            "SampleApp.Relations",
            "--tables",
            "users,posts",
            "--yes"
          ],
          env: [{"MIX_ENV", "dev"}],
          stderr_to_stdout: true
        )

      assert exit_code == 0, "Mix task failed with output: #{output}"

      # Verify both relations were created
      assert output =~ "SampleApp.Relations.Users"
      assert output =~ "SampleApp.Relations.Posts"
      refute output =~ "SampleApp.Relations.Comments"

      user_file = Path.join(@relations_path, "users.ex")
      post_file = Path.join(@relations_path, "posts.ex")
      comment_file = Path.join(@relations_path, "comments.ex")

      assert File.exists?(user_file)
      assert File.exists?(post_file)
      refute File.exists?(comment_file)

      # Verify content of both files
      user_content = File.read!(user_file)
      assert user_content =~ "defmodule SampleApp.Relations.Users do"
      assert user_content =~ "name: \"users\""

      post_content = File.read!(post_file)
      assert post_content =~ "defmodule SampleApp.Relations.Posts do"
      assert post_content =~ "name: \"posts\""
    end
  end
end
