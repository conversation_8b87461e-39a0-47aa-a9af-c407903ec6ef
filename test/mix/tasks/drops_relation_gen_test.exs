defmodule Mix.Tasks.Drops.Relation.GenTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.Generator

  describe "Gen relation verification" do
    test "generated relation content has correct structure" do
      # Test the Generator directly to verify relation module generation
      options = %{repo: "TestApp.Repo"}

      # Generate relation content as AST
      ast = Generator.generate_relation_module("users", "TestApp.Relations.Users", options)
      relation_content = Macro.to_string(ast)

      # Verify there's exactly one defmodule statement
      defmodule_count =
        relation_content
        |> String.split("\n")
        |> Enum.count(&String.contains?(&1, "defmodule"))

      assert defmodule_count == 1,
             "Expected exactly 1 defmodule, got #{defmodule_count}. Content:\n#{relation_content}"

      # Verify the content structure is correct
      assert relation_content =~ "defmodule TestApp.Relations.Users do"
      assert relation_content =~ "use Drops.Relation"
      assert relation_content =~ "repo: TestApp.Repo"
      assert relation_content =~ "name: \"users\""

      # Verify the generated content is valid Elixir code
      assert {_result, _bindings} = Code.eval_quoted(ast)
    end

    test "generated relation module body has correct format" do
      # Test the module body generation for use with Igniter
      options = %{repo: "TestApp.Repo"}

      # Generate relation body content (without defmodule wrapper)
      body_content = Generator.generate_relation_module_body("posts", "TestApp.Relations.Posts", options)

      # Verify the body content structure
      assert body_content =~ "use Drops.Relation"
      assert body_content =~ "repo: TestApp.Repo"
      assert body_content =~ "name: \"posts\""

      # Verify it doesn't contain defmodule (since it's just the body)
      refute body_content =~ "defmodule"

      # Verify it's valid Elixir code when wrapped in a module
      full_ast = quote do
        defmodule TestApp.Relations.Posts do
          unquote(Code.string_to_quoted!(body_content))
        end
      end

      assert {_result, _bindings} = Code.eval_quoted(full_ast)
    end

    test "generated relation string format is correct" do
      # Test the string generation function
      options = %{repo: "MyApp.Repo"}

      relation_string = Generator.generate_relation_module_string("comments", "MyApp.Relations.Comments", options)

      # Verify the string format
      assert relation_string =~ "defmodule MyApp.Relations.Comments do"
      assert relation_string =~ "use Drops.Relation, repo: MyApp.Repo, name: \"comments\""
      assert relation_string =~ "end"

      # Verify it's valid Elixir code
      ast = Code.string_to_quoted!(relation_string)
      assert {_result, _bindings} = Code.eval_quoted(ast)
    end

    test "handles different module name formats" do
      options = %{repo: "TestApp.Repo"}

      # Test with string module name
      ast1 = Generator.generate_relation_module("users", "TestApp.Relations.Users", options)
      content1 = Macro.to_string(ast1)
      assert content1 =~ "defmodule TestApp.Relations.Users do"

      # Test with atom module name
      ast2 = Generator.generate_relation_module("users", TestApp.Relations.Users, options)
      content2 = Macro.to_string(ast2)
      assert content2 =~ "defmodule TestApp.Relations.Users do"

      # Both should generate equivalent content
      assert content1 == content2
    end

    test "handles different repo name formats" do
      # Test with string repo name
      options1 = %{repo: "TestApp.Repo"}
      ast1 = Generator.generate_relation_module("users", "TestApp.Relations.Users", options1)
      content1 = Macro.to_string(ast1)
      assert content1 =~ "repo: TestApp.Repo"

      # Test with atom repo name (should work the same way)
      options2 = %{repo: TestApp.Repo}
      ast2 = Generator.generate_relation_module("users", "TestApp.Relations.Users", options2)
      content2 = Macro.to_string(ast2)
      assert content2 =~ "repo: TestApp.Repo"
    end

    test "relation module generation with complex table names" do
      options = %{repo: "TestApp.Repo"}

      # Test with underscore table name
      ast = Generator.generate_relation_module("user_profiles", "TestApp.Relations.UserProfiles", options)
      content = Macro.to_string(ast)

      assert content =~ "defmodule TestApp.Relations.UserProfiles do"
      assert content =~ "use Drops.Relation"
      assert content =~ "repo: TestApp.Repo"
      assert content =~ "name: \"user_profiles\""

      # Verify it compiles
      assert {_result, _bindings} = Code.eval_quoted(ast)
    end
  end
end
